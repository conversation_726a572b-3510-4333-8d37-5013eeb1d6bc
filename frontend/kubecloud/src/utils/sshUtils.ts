/**
 * SSH key generation utilities using jsrsasign for proper formatting
 */

import * as jsrsasign from 'jsrsasign'

export function arrayBufferToBase64(buffer: ArrayBuffer): string {
  const bytes = new Uint8Array(buffer)
  let binary = ''
  for (let i = 0; i < bytes.byteLength; i++) {
    binary += String.fromCharCode(bytes[i])
  }
  return btoa(binary)
}

export async function generateSSHECDSAKey(keyPair: CryptoKeyPair): Promise<string> {
  const spki = await crypto.subtle.exportKey('spki', keyPair.publicKey)
  const pem = arrayBufferToPem(spki, 'PUBLIC KEY')
  const sshKey = jsrsasign.KEYUTIL.getSSHKeyFromPublicKeyPEM(pem)
  return sshKey
}

export async function generateSSHRSAKey(keyPair: CryptoKeyPair): Promise<string> {
  const spki = await crypto.subtle.exportKey('spki', keyPair.publicKey)
  const pem = arrayBufferToPem(spki, 'PU<PERSON><PERSON> KEY')
  const sshKey = jsrsasign.KEYUTIL.getSSHKeyFromPublicKeyPEM(pem)
  return sshKey
}

export function arrayBufferToPem(buffer: ArrayBuffer, label: string): string {
  const base64 = arrayBufferToBase64(buffer)
  const lines = base64.match(/.{1,64}/g) || []
  return `-----BEGIN ${label}-----\n${lines.join('\n')}\n-----END ${label}-----\n`
}
